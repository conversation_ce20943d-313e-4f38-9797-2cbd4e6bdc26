import type { IVideo } from './interfaces/video';

export type IStorage = {
	videos: { [key: string]: IVideo };
	includedUrls: string[];
	isLight: boolean;
	rewindTime?: number; // in seconds
	forwardTime?: number; // in seconds
	alwaysLoop?: boolean;
	lastSync?: number;
	alwaysShuffle?: boolean;
	autoSkipAd?: boolean;
	// 新增保存路径配置
	downloadPath?: string; // 自定义下载路径，默认为系统下载文件夹
	createVideoFolder?: boolean; // 批量下载时是否创建视频文件夹，默认true
	// 新增浏览器选择配置
	preferredBrowser?: string; // 用户手动选择的浏览器，用于不支持的浏览器或覆盖自动检测
	enableCookiesByDefault?: boolean; // 是否默认启用cookie选项
};

export const storageDriver = {
	get: async (): Promise<IStorage> => {
		try {
			const value = await chrome.storage.sync.get();
			const storage: IStorage = {
				videos: value.videos ?? {},
				includedUrls: value.includedUrls ?? [],
				isLight: value.isLight,
				rewindTime: value.rewindTime ?? 10,
				forwardTime: value.forwardTime ?? 10,
				alwaysLoop: value.alwaysLoop ?? false,
				lastSync: value.lastSync ?? 0,
				alwaysShuffle: value.alwaysShuffle,
				autoSkipAd: value.autoSkipAd ?? false,
				downloadPath: value.downloadPath ?? '', // 默认为空，使用系统下载文件夹
				createVideoFolder: value.createVideoFolder ?? true, // 默认创建视频文件夹
				preferredBrowser: value.preferredBrowser ?? '', // 用户偏好浏览器
				enableCookiesByDefault: value.enableCookiesByDefault ?? false // 默认不启用cookie
			};
			return storage;
		} catch (error) {
			throw error;
		}
	},
	set: (value: IStorage): Promise<void> => {
		return chrome.storage.sync.set(value);
	},
};
