<script lang="ts">
	import { onMount } from 'svelte';
	import { secondToTimeString } from '../../utils/second-to-time-string';
	import { timeStringToSecond } from '../../utils/time-string-to-second';
	import { storage } from '../stores/storage';
	import ClipTime from './clip-time.svelte';
	import type { IClipTime } from '../interfaces/clip-time';
	import type { IVideoClip } from '../../interfaces/clip-time';
	import AddButton from './add-button.svelte';
	import RegionSelector from './region-selector.svelte';
	import type { IDelogoRegion } from '../../interfaces/video-preview';
	import { calculatePreviewDimensions } from '../../interfaces/video-preview';
	import { getBrowserDetectionInfo } from '../../utils/yt-dlp-generator';

	export let tab: chrome.tabs.Tab;
	export let id: string;

	// Delogo 区域选择相关状态
	let showRegionSelector = false;
	let delogoRegions: IDelogoRegion[] = [];
	let videoWidth = 1920;
	let videoHeight = 1080;

	// 预览窗口尺寸配置
	const MAX_PREVIEW_WIDTH = 800;
	const MAX_PREVIEW_HEIGHT = 450;

	// 计算动态预览尺寸，保持视频宽高比
	$: previewDimensions = calculatePreviewDimensions(
		videoWidth,
		videoHeight,
		MAX_PREVIEW_WIDTH,
		MAX_PREVIEW_HEIGHT
	);

	// 编辑模式相关状态
	let editMode = false;
	let editingClip: IVideoClip | null = null;
	let editingClipIndex = -1;
	let editingClipName = '';
	let hasUnsavedChanges = false; // 跟踪是否有未保存的更改

	// 浏览器检测信息
	let browserInfo = getBrowserDetectionInfo();

	let clips: IClipTime[] = [
		{
			start: '',
			end: '',
			name: '',
		},
	];

	let message = '';

	$: isCanSave = !!clips.find((clip) => clip.start || clip.end || clip.name.trim());

	onMount(async () => {
		// 检查是否为编辑模式
		const editInfo = sessionStorage.getItem('clipperEditInfo');
		if (editInfo) {
			try {
				const parsedEditInfo = JSON.parse(editInfo);
				if (parsedEditInfo.mode === 'edit' && parsedEditInfo.videoId === id) {
					editMode = true;
					editingClip = parsedEditInfo.clip;
					editingClipIndex = parsedEditInfo.clipIndex;
					editingClipName = editingClip?.name || '';

					// 从片段中加载delogo区域配置
					if (editingClip?.delogoRegions) {
						delogoRegions = [...editingClip.delogoRegions];
					} else {
						delogoRegions = [];
					}

					// 自动打开delogo界面
					showRegionSelector = true;

					// 清除sessionStorage中的编辑信息
					sessionStorage.removeItem('clipperEditInfo');
				}
			} catch (error) {
				console.error('解析编辑信息失败:', error);
			}
		}

		// 新的工作流程：剪藏页面始终显示空的输入框
		// 已保存的片段只在"记录"页面显示
		const video = $storage.videos[id];
		if (video) {
			// 只加载视频尺寸，不加载delogo区域（因为delogo现在是片段级别的）
			if (video.videoWidth && video.videoHeight) {
				videoWidth = video.videoWidth;
				videoHeight = video.videoHeight;
			}
		}

		// 更新浏览器检测信息
		browserInfo = getBrowserDetectionInfo();
	});

	async function saveVideo() {
		const videoClips: IVideoClip[] = [];
		for (let idx = 0; idx < clips.length; idx++) {
			const clip = clips[idx];
			const canSave = clip.start || clip.end || clip.name.trim() || loop;
			if (!canSave) {
				continue;
			}
			const startSeconds = timeStringToSecond(clip.start);
			if (clip.start && startSeconds === -1) {
				message = 'Start time is not valid';
				return;
			}

			const endSeconds = timeStringToSecond(clip.end);
			if (clip.end && endSeconds === -1) {
				message = 'End time is not valid';
				return;
			}

			if (clip.start && clip.end && endSeconds <= startSeconds) {
				message = 'End time should be greater than start time';
				return;
			}

			if (idx > 0) {
				if (startSeconds <= videoClips[idx - 1].end || videoClips[idx - 1].end === -1) {
					message = `Start must be greater than the end of prev clip`;
					return;
				}
			}

			videoClips.push({
				start: startSeconds,
				end: endSeconds,
				name: clip.name.trim() || `片段${idx + 1}`,  // 如果没有名称，使用默认名称
				delogoRegions: []  // 新片段默认没有delogo区域
			});
		}

		storage.update((prev) => {
			prev.lastSync = new Date().getTime();

			// 如果视频已存在，进行去重合并
			if (prev.videos[id]) {
				const existingClips = prev.videos[id].clips;
				const mergedClips = [...existingClips];

				// 对新片段进行去重检查（只有开始和结束时间完全相同才视为重复）
				for (const newClip of videoClips) {
					const isDuplicate = existingClips.some(existingClip =>
						existingClip.start === newClip.start &&
						existingClip.end === newClip.end
					);

					if (!isDuplicate) {
						mergedClips.push(newClip);
					}
				}

				// 按开始时间排序
				mergedClips.sort((a, b) => a.start - b.start);

				prev.videos[id] = {
					...prev.videos[id],
					clips: mergedClips,
					videoWidth: videoWidth,       // 保存视频宽度
					videoHeight: videoHeight      // 保存视频高度
				};
			} else {
				// 新视频，直接保存
				prev.videos[id] = {
					id,
					title: tab.title!,
					clips: videoClips,
					videoWidth: videoWidth,       // 保存视频宽度
					videoHeight: videoHeight      // 保存视频高度
				};
			}
			return prev;
		});

		// 保存成功后清空所有输入框，等待下一次输入
		clearUnsavedClips();
		message = 'Saved';
	}

	// 修改为只清除未保存的片段输入，不删除已保存的视频记录
	function clearUnsavedClips() {
		// 重置所有片段输入框为空
		for (let i = 0; i < clips.length; i++) {
			clips[i].start = '';
			clips[i].end = '';
			clips[i].name = '';
		}
		// 重置为只有一个空片段
		clips = [{
			start: '',
			end: '',
			name: '',
		}];
		message = 'Cleared unsaved clips';
	}

	function addClip() {
		clips.push({
			end: '',
			start: '',
			name: '',
		});
		clips = [...clips];
	}

	/**
	 * 打开区域选择器
	 */
	function openRegionSelector() {
		showRegionSelector = true;
	}

	/**
	 * 关闭区域选择器
	 */
	function closeRegionSelector() {
		showRegionSelector = false;

		// 如果是编辑模式，重置编辑状态并返回记录页面
		if (editMode) {
			editMode = false;
			editingClip = null;
			editingClipIndex = -1;
			editingClipName = '';

			// 通知父组件切换回记录页面
			window.dispatchEvent(new CustomEvent('switchToRecords'));
		}
	}

	/**
	 * 处理区域添加
	 */
	function handleRegionAdd(event: CustomEvent) {
		const region = event.detail as IDelogoRegion;
		delogoRegions = [...delogoRegions, region];
		message = `已添加区域: ${region.name}`;
		hasUnsavedChanges = true; // 标记有未保存的更改

		// 在编辑模式下，区域只在内存中保存，不立即写入storage
		// 只有在saveClipChanges中才会保存到片段级别
	}

	/**
	 * 处理区域删除
	 */
	function handleRegionDelete(event: CustomEvent) {
		const regionId = event.detail as string;
		delogoRegions = delogoRegions.filter(r => r.id !== regionId);
		message = '已删除区域';
		hasUnsavedChanges = true; // 标记有未保存的更改

		// 在编辑模式下，区域只在内存中删除，不立即写入storage
		// 只有在saveClipChanges中才会保存到片段级别
	}

	/**
	 * 处理区域切换
	 */
	function handleRegionToggle(event: CustomEvent) {
		const { regionId, enabled } = event.detail;
		const region = delogoRegions.find(r => r.id === regionId);
		if (region) {
			region.enabled = enabled;
			delogoRegions = [...delogoRegions];
			message = `区域 ${region.name} ${enabled ? '已启用' : '已禁用'}`;
			hasUnsavedChanges = true; // 标记有未保存的更改

			// 在编辑模式下，区域只在内存中切换，不立即写入storage
			// 只有在saveClipChanges中才会保存到片段级别
		}
	}

	/**
	 * 清除所有区域
	 */
	function handleRegionsClear() {
		delogoRegions = [];
		message = '已清除所有区域';
		hasUnsavedChanges = true; // 标记有未保存的更改

		// 在编辑模式下，区域只在内存中清除，不立即写入storage
		// 只有在saveClipChanges中才会保存到片段级别
	}



	/**
	 * 处理视频尺寸更新
	 */
	function handleVideoSizeUpdate(event: CustomEvent) {
		const { width, height } = event.detail;
		videoWidth = width;
		videoHeight = height;

		// 在编辑模式下不显示视频尺寸检测消息，避免干扰用户
		if (!editMode) {
			message = `检测到视频尺寸: ${width}×${height}`;
		}

		// 在编辑模式下，视频尺寸只在内存中更新，不立即写入storage
		// 只有在saveClipChanges中才会保存到存储中
		// 这样可以避免频繁的Firebase写入操作
	}

	/**
	 * 处理视频捕获开始
	 */
	function handleCaptureStart() {
		message = '视频预览已启动';
	}

	/**
	 * 处理区域选择器错误
	 */
	function handleRegionError(event: CustomEvent) {
		const errorMessage = event.detail as string;
		message = `错误: ${errorMessage}`;
	}

	/**
	 * 保存片段修改（重命名和delogo区域）
	 */
	function saveClipChanges() {
		if (!editMode || !editingClip || editingClipIndex === -1) {
			message = '编辑信息无效';
			console.error('保存失败：编辑信息无效', { editMode, editingClip, editingClipIndex });
			return;
		}



		// 更新存储中的片段信息
		storage.update((prev) => {
			if (prev.videos[id] && prev.videos[id].clips && prev.videos[id].clips[editingClipIndex]) {
				// 更新片段名称
				if (editingClipName.trim()) {
					prev.videos[id].clips[editingClipIndex].name = editingClipName.trim();
				}

				// 将delogo区域配置保存到片段级别
				prev.videos[id].clips[editingClipIndex].delogoRegions = [...delogoRegions];

				// 更新视频尺寸（保留在视频级别，用于预览）
				prev.videos[id].videoWidth = videoWidth;
				prev.videos[id].videoHeight = videoHeight;

				prev.lastSync = new Date().getTime();
			}
			return prev;
		});

		message = '片段修改已保存';
		hasUnsavedChanges = false; // 重置未保存更改标志

		// 关闭编辑模式并返回记录页面
		editMode = false;
		editingClip = null;
		editingClipIndex = -1;
		editingClipName = '';
		closeRegionSelector();

		// 通知父组件切换回记录页面
		window.dispatchEvent(new CustomEvent('switchToRecords'));
	}


</script>

<div class="w-full">
	<div class="flex gap-2 items-start">
		<img width="20px" src={tab.favIconUrl} alt="icon" />
		<span class="text-sm font-medium">{tab.title}</span>
	</div>
	<div class="flex justify-between items-center mt-4">
		<div class="flex gap-3 items-center">
			<!-- Save 按钮 - 紫色渐变 -->
			<button
				disabled={!isCanSave}
				on:click={saveVideo}
				class="px-6 py-2 rounded-xl font-semibold text-white shadow-lg hover:shadow-xl
					   transform hover:-translate-y-0.5 transition-all duration-200
					   flex items-center justify-center active:shadow-inner active:transform-none
					   {isCanSave ? 'bg-gradient-to-br from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800' : 'bg-gradient-to-br from-gray-400 to-gray-600 cursor-not-allowed opacity-60'}"
				title="保存片段">
				Save
			</button>

			<!-- Clear 按钮 - 橙色渐变 -->
			<button
				on:click={clearUnsavedClips}
				class="px-6 py-2 rounded-xl font-semibold text-white shadow-lg hover:shadow-xl
					   transform hover:-translate-y-0.5 transition-all duration-200
					   flex items-center justify-center active:shadow-inner active:transform-none
					   bg-gradient-to-br from-orange-500 to-orange-700 hover:from-orange-600 hover:to-orange-800"
				title="清除未保存的片段输入">
				Clear
			</button>



			{#if message != ''}
				<p class="ml-3 text-green-500 font-medium text-sm">{message}</p>
			{/if}
		</div>
	</div>
	<div class="flex flex-col mb-1 mt-2 gap-2 items-start">
		{#each clips as clip}
			<div class="w-full">
				<ClipTime bind:clip />
			</div>
		{/each}
		<div class="w-full">
			<AddButton onClick={addClip} />
		</div>
	</div>



	<!-- 浏览器检测信息显示 -->
	{#if browserInfo}
		<div class="mt-3 p-2 bg-blue-50 dark:bg-blue-900 rounded-md">
			<div class="flex items-center gap-2 text-sm text-blue-800 dark:text-blue-200">
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
				</svg>
				<span>
					浏览器: {browserInfo.browser}
					{#if browserInfo.isSupported}
						<span class="text-green-600 dark:text-green-400">✓ 支持自动cookie</span>
					{:else}
						<span class="text-yellow-600 dark:text-yellow-400">⚠ 需手动选择</span>
					{/if}
				</span>
			</div>
		</div>
	{/if}
</div>

<!-- 区域选择器模态框 -->
{#if showRegionSelector}
	<div class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
		<div class="bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-4xl w-full max-h-[95vh] overflow-hidden flex flex-col">
			<!-- 模态框头部 -->
			<div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
				<h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
					{editMode ? '修改片段' : '设置去logo区域'}
				</h2>
				<button
					on:click={closeRegionSelector}
					class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- 模态框主体 -->
			<div class="flex-1 overflow-auto p-4">
				<!-- 画面预览窗口（移到顶部） -->
				<div class="mb-6">
					<RegionSelector
						{videoWidth}
						{videoHeight}
						previewWidth={previewDimensions.width}
						previewHeight={previewDimensions.height}
						regions={delogoRegions}
						tabId={tab.id}
						on:regionAdd={handleRegionAdd}
						on:regionDelete={handleRegionDelete}
						on:regionToggle={handleRegionToggle}
						on:regionsClear={handleRegionsClear}
						on:videoSizeUpdate={handleVideoSizeUpdate}
						on:captureStart={handleCaptureStart}
						on:error={handleRegionError}
					/>
				</div>

				<!-- 片段重命名功能板块（仅在编辑模式下显示） -->
				{#if editMode}
					<div class="mb-6 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700
								rounded-xl p-4 border border-gray-200 dark:border-gray-600">
						<h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
							📝 片段重命名
						</h3>
						<div class="flex items-center gap-3">
							<input
								type="text"
								bind:value={editingClipName}
								placeholder="输入新的片段名称 (非必填)"
								class="flex-1 px-3 py-2 rounded-lg bg-white dark:bg-gray-800
									   border border-gray-300 dark:border-gray-600
									   text-gray-700 dark:text-gray-200
									   placeholder-gray-500 dark:placeholder-gray-400
									   focus:outline-none focus:ring-2 focus:ring-purple-500
									   transition-all duration-200"
								on:input={() => hasUnsavedChanges = true}
							/>
							<div class="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
								当前: {editingClip?.name || `片段${editingClipIndex + 1}`}
							</div>
						</div>
					</div>
				{/if}

			</div>

			<!-- 模态框底部 -->
			<div class="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
				<div class="flex gap-2">
					{#if editMode && hasUnsavedChanges}
						<button
							on:click={saveClipChanges}
							class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700
								   transition-all duration-200 shadow-md hover:shadow-lg
								   transform hover:-translate-y-0.5"
						>
							💾 保存修改
						</button>
					{/if}
					<button
						on:click={closeRegionSelector}
						class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700
							   transition-all duration-200 shadow-md hover:shadow-lg
							   transform hover:-translate-y-0.5"
					>
						{editMode ? '取消' : '完成'}
					</button>
				</div>
			</div>
		</div>
	</div>
{/if}
