<script lang="ts">
	import { onMount } from 'svelte';
	import CutIcon from '../assets/svg/cut-icon.svelte';
	import SettingIcon from '../assets/svg/setting-icon.svelte';
	import VolumeIcon from '../assets/svg/volume-icon.svelte';
	import { storageDriver, type IStorage } from '../storage-driver';
	import AudioController from './pages/audio-controller.svelte';
	import ClipperPage from './pages/clipper-page.svelte';
	import { storage } from './stores/storage';
	import MoonIcon from '../assets/svg/moon-icon.svelte';
	import SunIcon from '../assets/svg/sun-icon.svelte';
	import { onAuthStateChanged } from 'firebase/auth';
	import { auth, db } from './utils/firebase';
	import { authUser } from './stores/user-store';
	import ExitIcon from '../assets/svg/exit-icon.svelte';
	import { DocumentReference, doc, getDoc, setDoc } from 'firebase/firestore';
	import type { IVideo } from '../interfaces/video';

	let init = false;
	let isLight: boolean = false;
	let currentTab: 'clipper' | 'media' | 'records' = 'clipper';
	let loadingUser = true;

	// 动态导入大型组件
	let VideoRecords: any = null;
	
	async function loadVideoRecords() {
		if (!VideoRecords) {
			const module = await import('./pages/video-records.svelte');
			VideoRecords = module.default;
		}
	}

	// 监听tab切换，预加载需要的组件
	$: if (currentTab === 'records' && !VideoRecords) {
		loadVideoRecords();
	}

	const updateTheme = () => {
		if (!init) {
			return;
		}
		storage.update((prev) => {
			prev.isLight = isLight;
			return prev;
		});
		if (isLight) {
			document.body.classList.remove('dark');
		} else {
			document.body.classList.add('dark');
		}
	};

	// 防抖同步函数，避免频繁写入Firebase
	let syncTimeout: number | null = null;
	let lastSyncTime = 0;
	const SYNC_DEBOUNCE_DELAY = 3000; // 增加到3秒
	const MIN_SYNC_INTERVAL = 5000; // 最小同步间隔5秒

	const sync = async (newStorage: IStorage) => {
		if (!$authUser) {
			return;
		}

		const now = Date.now();

		// 如果距离上次同步时间太短，延长防抖时间
		const timeSinceLastSync = now - lastSyncTime;
		const debounceDelay = timeSinceLastSync < MIN_SYNC_INTERVAL ?
			SYNC_DEBOUNCE_DELAY + (MIN_SYNC_INTERVAL - timeSinceLastSync) :
			SYNC_DEBOUNCE_DELAY;

		// 清除之前的定时器
		if (syncTimeout) {
			clearTimeout(syncTimeout);
		}

		// 设置新的定时器，使用动态延迟时间
		syncTimeout = setTimeout(async () => {
			try {
				const docRef = doc(db, 'clipper', $authUser.uid) as DocumentReference<IStorage>;
				await setDoc(docRef, newStorage);
				console.log('Firebase sync completed');
				lastSyncTime = Date.now();
			} catch (error) {
				console.log('Firebase sync error:', error);
				// 如果是配额错误，增加更长的延迟
				if (error.message && error.message.includes('MAX_WRITE_OPERATIONS_PER_MINUTE')) {
					console.warn('Firebase write quota exceeded, will retry later');
					lastSyncTime = Date.now() + 60000; // 延迟1分钟再尝试
				}
			}
			syncTimeout = null;
		}, debounceDelay);
	};

	$: isLight, updateTheme();
	$: $storage,
		(async () => {
			if (!init) {
				return;
			}
			await storageDriver.set($storage);
			sync($storage);
		})();

	onMount(async () => {
		const res = await storageDriver.get();
		storage.set(res);
		isLight = res.isLight;
		init = true;

		// 获取当前标签页URL
		const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

		// 检测是否为受支持的网站（优先检测YouTube，然后检查用户配置的URL）
		let isSupportedSite = false;
		if (tab.url) {
			// 首先检测是否为YouTube网站
			const isYouTubeSite = tab.url.includes('youtube.com') || tab.url.includes('youtu.be');

			// 然后检查用户配置的URL模式
			const isConfiguredSite = res.includedUrls.some((pattern: string) => {
				try {
					return new RegExp(pattern).test(tab.url);
				} catch {
					return false;
				}
			});

			isSupportedSite = isYouTubeSite || isConfiguredSite;
		}

		// 检查当前页面是否在记录中
		let isInRecords = false;
		if (tab.url && tab.url.includes('youtube.com/watch')) {
			const regex = /[?&]v=([^&#]+)/;
			const match = tab.url.match(regex);
			const videoId = match && match[1];
			if (videoId && res.videos[videoId]) {
				isInRecords = true;
			}
		}

		// 设置默认页面逻辑：
		// 1. 先检测当前处于的页面是否在记录中，如果是的话，优先打开记录界面
		// 2. 当检测到处于受支持的网页，但是不在记录中，默认打开剪藏页面
		// 3. 其余页面默认打开媒体页面
		if (isInRecords) {
			currentTab = 'records';
		} else if (isSupportedSite) {
			currentTab = 'clipper';
		} else {
			currentTab = 'media';
		}

		// 监听来自records页面的切换到clipper页面的事件
		window.addEventListener('switchToClipper', () => {
			currentTab = 'clipper';
		});

		// 监听来自clipper页面的切换回records页面的事件
		window.addEventListener('switchToRecords', () => {
			currentTab = 'records';
		});
	});

	onAuthStateChanged(auth, async (user) => {
		loadingUser = false;
		console.log('USER', user);
		authUser.set(user);
		if (!user) {
			return;
		}
		try {
			const docRef = doc(db, 'clipper', user.uid) as DocumentReference<IStorage>;
			const docSnap = await getDoc(docRef);
			const synced = docSnap.data();
			if (!synced) {
				sync($storage);
				return;
			}
			if (synced.lastSync === $storage.lastSync) {
				return;
			}
			const videos: { [key: string]: IVideo } = {};
			for (let id of Object.keys(synced.videos)) {
				videos[id] = synced.videos[id];
			}
			for (let id of Object.keys($storage.videos)) {
				videos[id] = $storage.videos[id];
			}
			const cloudBehind = synced.lastSync! < $storage.lastSync!;
			storage.update((prev) => {
				return {
					videos,
					lastSync: new Date().getTime(),
					includedUrls: cloudBehind ? prev.includedUrls : synced.includedUrls,
					isLight: cloudBehind ? prev.isLight : synced.isLight,
					alwaysLoop: cloudBehind ? prev.alwaysLoop : synced.alwaysLoop,
					forwardTime: cloudBehind ? prev.forwardTime : synced.forwardTime,
					rewindTime: cloudBehind ? prev.rewindTime : synced.rewindTime,
				};
			});
		} catch (error) {
			console.log('ERROR', error);
		}
	});
</script>

<main class="min-w-[550px] max-h-[600px] p-4 bg-light dark:bg-dark flex flex-col items-center text-dark dark:text-light overflow-y-auto">
	<div class="flex items-center gap-3 w-full p-2 bg-gray-100 dark:bg-gray-800 rounded-xl shadow-inner">
		<!-- 主题切换按钮 - 轻拟物风格 -->
		<button
			on:click={() => {
				isLight = !isLight;
			}}
			class="p-2 rounded-lg bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800
				   shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200
				   fill-yellow-500 dark:fill-yellow-400 hover:fill-yellow-600 dark:hover:fill-yellow-300
				   active:shadow-inner active:transform-none"
			title={isLight ? '切换到深色模式' : '切换到浅色模式'}
		>
			{#if isLight}
				<MoonIcon width={18} />
			{:else}
				<SunIcon width={18} />
			{/if}
		</button>

		<!-- 设置按钮 - 轻拟物风格 -->
		<a
			href="../options/index.html"
			target="_blank"
			class="p-2 rounded-lg bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800
				   shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200
				   fill-orange-500 dark:fill-orange-400 hover:fill-orange-600 dark:hover:fill-orange-300
				   active:shadow-inner active:transform-none inline-block"
			title="打开设置页面"
		>
			<SettingIcon width={18} />
		</a>

		<!-- 三个标签页按钮 - 轻拟物风格 -->
		<div class="flex gap-2 flex-1">
			<button
				on:click={() => {
					currentTab = 'clipper';
				}}
				class={`flex-1 py-2 px-3 rounded-xl font-semibold text-sm transition-all duration-200
						flex items-center justify-center gap-2 min-w-[80px] transform hover:-translate-y-0.5
						${currentTab === 'clipper'
							? 'bg-gradient-to-br from-teal-400 to-teal-600 text-white shadow-lg hover:shadow-xl fill-white active:shadow-inner active:transform-none'
							: 'bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 text-gray-700 dark:text-gray-300 shadow-md hover:shadow-lg fill-teal-500 dark:fill-teal-400'
						}`}
				title="视频剪藏功能"
			>
				<CutIcon width={16} />
				<span>剪藏</span>
			</button>

			<button
				on:click={() => {
					currentTab = 'media';
				}}
				class={`flex-1 py-2 px-3 rounded-xl font-semibold text-sm transition-all duration-200
						flex items-center justify-center gap-2 min-w-[80px] transform hover:-translate-y-0.5
						${currentTab === 'media'
							? 'bg-gradient-to-br from-red-400 to-red-600 text-white shadow-lg hover:shadow-xl fill-white active:shadow-inner active:transform-none'
							: 'bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 text-gray-700 dark:text-gray-300 shadow-md hover:shadow-lg fill-red-500 dark:fill-red-400'
						}`}
				title="媒体控制功能"
			>
				<VolumeIcon width={16} />
				<span>媒体</span>
			</button>

			<button
				on:click={() => {
					currentTab = 'records';
				}}
				class={`flex-1 py-2 px-3 rounded-xl font-semibold text-sm transition-all duration-200
						flex items-center justify-center gap-2 min-w-[80px] transform hover:-translate-y-0.5
						${currentTab === 'records'
							? 'bg-gradient-to-br from-blue-400 to-blue-600 text-white shadow-lg hover:shadow-xl fill-white active:shadow-inner active:transform-none'
							: 'bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 text-gray-700 dark:text-gray-300 shadow-md hover:shadow-lg fill-blue-500 dark:fill-blue-400'
						}`}
				title="视频记录管理"
			>
				<!-- 使用SVG图标表示记录 -->
				<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
					<path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
				</svg>
				<span>记录</span>
			</button>
		</div>
		{#if $authUser || loadingUser}
			<button
				title="退出登录"
				on:click={async () => {
					if (loadingUser) return;
					const yes = confirm('确定要退出登录吗？');
					if (!yes) {
						return;
					}
					await auth.signOut();
				}}
				class="p-2 rounded-lg bg-gradient-to-br from-red-200 to-red-300 dark:from-red-700 dark:to-red-800
					   shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200
					   fill-red-600 dark:fill-red-400 hover:fill-red-700 dark:hover:fill-red-300
					   active:shadow-inner active:transform-none"
			>
				<ExitIcon width={18} height={18} />
			</button>
		{/if}
	</div>
	<!-- 轻拟物风格分割线 -->
	<div class="w-full mt-3 mb-3 px-2">
		<div class="h-[1px] bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent shadow-sm" />
	</div>
	{#if currentTab === 'clipper'}
		<ClipperPage />
	{:else if currentTab === 'media'}
		<AudioController />
	{:else if currentTab === 'records'}
		{#if VideoRecords}
			<svelte:component this={VideoRecords} />
		{:else}
			<div class="flex items-center justify-center p-8">
				<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
				<span class="ml-2 text-gray-600 dark:text-gray-400">加载中...</span>
			</div>
		{/if}
	{/if}
</main>
