import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
	apiKey: 'AIzaSyBRl0r-ZwEISGOMelE8VjUSioaY-FHn5zs',
	authDomain: 'clips-88ad5.firebaseapp.com',
	projectId: 'clips-88ad5',
	storageBucket: 'clips-88ad5.appspot.com',
	messagingSenderId: '298998233112',
	appId: '1:298998233112:web:84faf36022a69b750f2b6d',
};

// 懒加载Firebase实例
let app: any = null;
let db: any = null;
let auth: any = null;

export function getFirebaseApp() {
	if (!app) {
		app = initializeApp(firebaseConfig);
	}
	return app;
}

export function getDB() {
	if (!db) {
		db = getFirestore(getFirebaseApp());
	}
	return db;
}

export function getAuthInstance() {
	if (!auth) {
		auth = getAuth(getFirebaseApp());
	}
	return auth;
}

// 为了兼容现有代码，保留原有的导出方式
export { getDB as db, getAuthInstance as auth };